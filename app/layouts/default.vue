<template>
  <div class="min-h-screen bg-brutal-white">
    <!-- Navigation Header -->
    <header class="mobile-nav border-brutal-heavy bg-brutal-white">
      <nav class="mx-auto max-w-7xl mobile-padding">
        <div class="flex h-16 items-center justify-between">
          <!-- Logo -->
          <div class="flex items-center">
            <NuxtLink to="/" class="font-brutal text-xl sm:text-2xl text-brutal-black hover-brutal-electric">
              DEFI.AI
            </NuxtLink>
          </div>

          <!-- Desktop Navigation -->
          <div class="hidden lg:block">
            <div class="ml-10 flex items-baseline space-x-2 xl:space-x-4">
              <NuxtLink
                to="/portfolio"
                class="border-brutal bg-brutal-white px-3 py-2 xl:px-4 font-brutal text-xs xl:text-sm uppercase text-brutal-black hover-brutal-neon touch-target"
              >
                PORTFOLIO
              </NuxtLink>
              <NuxtLink
                to="/strategies"
                class="border-brutal bg-brutal-white px-3 py-2 xl:px-4 font-brutal text-xs xl:text-sm uppercase text-brutal-black hover-brutal-cyan touch-target"
              >
                STRATEGIES
              </NuxtLink>
              <NuxtLink
                to="/analytics"
                class="border-brutal bg-brutal-white px-3 py-2 xl:px-4 font-brutal text-xs xl:text-sm uppercase text-brutal-black hover-brutal-magenta touch-target"
              >
                ANALYTICS
              </NuxtLink>
              <NuxtLink
                to="/history"
                class="border-brutal bg-brutal-white px-3 py-2 xl:px-4 font-brutal text-xs xl:text-sm uppercase text-brutal-black hover-brutal-pink touch-target"
              >
                HISTORY
              </NuxtLink>
            </div>
          </div>

          <!-- Wallet Connection Button -->
          <div class="hidden sm:flex items-center">
            <Button
              class="border-brutal-thick bg-hot-magenta px-4 py-2 sm:px-6 sm:py-3 font-brutal text-xs sm:text-sm uppercase text-brutal-white shadow-brutal hover-brutal-electric mobile-tap"
              @click="connectWallet"
            >
              <span class="hidden sm:inline">CONNECT </span>WALLET
            </Button>
          </div>

          <!-- Mobile menu button -->
          <div class="lg:hidden">
            <Sheet>
              <SheetTrigger as-child>
                <Button
                  class="border-brutal bg-brutal-white p-2 text-brutal-black hover-brutal-neon touch-target mobile-tap"
                >
                  <Icon name="lucide:menu" class="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" class="w-80 bg-brutal-black border-brutal-heavy">
                <SheetHeader>
                  <SheetTitle class="font-brutal text-2xl text-neon-lime">DEFI.AI</SheetTitle>
                </SheetHeader>
                <div class="mt-8 space-y-4">
                  <NuxtLink
                    to="/portfolio"
                    class="block border-brutal bg-neon-lime px-4 py-3 font-brutal text-sm uppercase text-brutal-black hover-brutal-electric mobile-tap"
                    @click="closeMobileMenu"
                  >
                    PORTFOLIO
                  </NuxtLink>
                  <NuxtLink
                    to="/strategies"
                    class="block border-brutal bg-neon-cyan px-4 py-3 font-brutal text-sm uppercase text-brutal-black hover-brutal-magenta mobile-tap"
                    @click="closeMobileMenu"
                  >
                    STRATEGIES
                  </NuxtLink>
                  <NuxtLink
                    to="/analytics"
                    class="block border-brutal bg-hot-magenta px-4 py-3 font-brutal text-sm uppercase text-brutal-white hover-brutal-electric mobile-tap"
                    @click="closeMobileMenu"
                  >
                    ANALYTICS
                  </NuxtLink>
                  <NuxtLink
                    to="/history"
                    class="block border-brutal bg-neon-pink px-4 py-3 font-brutal text-sm uppercase text-brutal-white hover-brutal-cyan mobile-tap"
                    @click="closeMobileMenu"
                  >
                    HISTORY
                  </NuxtLink>
                  <div class="pt-4">
                    <Button
                      class="w-full border-brutal-thick bg-electric-blue px-6 py-4 font-brutal text-sm uppercase text-brutal-white shadow-brutal hover-brutal-neon mobile-tap"
                      @click="connectWallet"
                    >
                      CONNECT WALLET
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>

      </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-1 nav-spacing">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="border-brutal-heavy bg-brutal-black">
      <div class="mx-auto max-w-7xl mobile-padding">
        <div class="mobile-grid gap-6 md:gap-8">
          <!-- Brand -->
          <div class="col-span-full md:col-span-1">
            <h3 class="font-brutal text-lg sm:text-xl text-electric-blue">DEFI.AI</h3>
            <p class="font-mono-brutal mt-2 mobile-text text-brutal-white">
              AI-POWERED DEFI DOMINATION
            </p>
          </div>

          <!-- Supported Chains -->
          <div class="md:col-span-1">
            <h4 class="font-brutal text-sm uppercase text-acid-green">SUPPORTED CHAINS</h4>
            <div class="mt-4 space-y-2">
              <div class="border-brutal bg-neon-lime px-3 py-2 font-mono-brutal text-xs text-brutal-black shadow-brutal">
                ETHEREUM
              </div>
              <div class="border-brutal bg-brutal-charcoal px-3 py-2 font-mono-brutal text-xs text-brutal-white opacity-50">
                POLYGON (SOON)
              </div>
              <div class="border-brutal bg-brutal-charcoal px-3 py-2 font-mono-brutal text-xs text-brutal-white opacity-50">
                BASE (SOON)
              </div>
            </div>
          </div>

          <!-- Protocols -->
          <div class="md:col-span-1">
            <h4 class="font-brutal text-sm uppercase text-hot-magenta">PROTOCOLS</h4>
            <div class="mt-4 space-y-2">
              <div class="border-brutal bg-neon-cyan px-3 py-2 font-mono-brutal text-xs text-brutal-black shadow-brutal">
                AAVE
              </div>
              <div class="border-brutal bg-neon-pink px-3 py-2 font-mono-brutal text-xs text-brutal-white shadow-brutal">
                UNISWAP V3
              </div>
              <div class="border-brutal bg-neon-orange px-3 py-2 font-mono-brutal text-xs text-brutal-black shadow-brutal">
                LIDO
              </div>
            </div>
          </div>

          <!-- Links -->
          <div class="md:col-span-1">
            <h4 class="font-brutal text-sm uppercase text-plasma-orange">LINKS</h4>
            <div class="mt-4 space-y-2">
              <NuxtLink
                to="/docs"
                class="block font-mono-brutal mobile-text text-brutal-white hover:text-acid-green transition-colors duration-100"
              >
                DOCUMENTATION
              </NuxtLink>
              <NuxtLink
                to="/support"
                class="block font-mono-brutal mobile-text text-brutal-white hover:text-electric-blue transition-colors duration-100"
              >
                SUPPORT
              </NuxtLink>
              <NuxtLink
                to="/api"
                class="block font-mono-brutal mobile-text text-brutal-white hover:text-hot-magenta transition-colors duration-100"
              >
                API ACCESS
              </NuxtLink>
            </div>
          </div>
        </div>

        <!-- Copyright -->
        <Separator class="my-8 bg-neon-lime" />
        <div class="pb-4">
          <p class="font-mono-brutal text-center mobile-text text-brutal-white">
            © 2024 <span class="text-electric-blue">DEFI.AI</span> - BUILT FOR THE FUTURE OF FINANCE
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// Wallet connection (placeholder)
const connectWallet = () => {
  // TODO: Implement wallet connection logic
  console.log('Connect wallet clicked');
};

// Close mobile menu function for sheet
const closeMobileMenu = () => {
  // The sheet will handle its own closing
  console.log('Mobile menu closed');
};
</script>
